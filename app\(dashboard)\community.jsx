import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, Image, RefreshControl } from 'react-native';
import ThemedText from "../../components/ThemedText";
import ThemedView from "../../components/ThemedView";
import { Colors } from "../../constants/Colors";
import { useRouter, useLocalSearchParams } from 'expo-router';
import ProfileSidebar from "../../components/ProfileSidebar";
import { LinearGradient } from 'expo-linear-gradient';
import { useGuestPermission } from '../../hooks/useGuestPermission';
import GuestPermissionModal from '../../components/GuestPermissionModal';
import PostCard from '../../components/PostCard';

// PostCard组件现在包含所有帖子相关的逻辑和UI

const Community = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [posts, setPosts] = useState([]); // 当前显示的帖子
  const [filterTag, setFilterTag] = useState(null); // 当前筛选的标签
  // 点赞状态现在由PostCard组件管理
  const router = useRouter();
  const params = useLocalSearchParams();

  // 权限管理
  const {
    wrapGuestAction,
    showPermissionModal,
    currentFeature,
    handleRegister,
    closePermissionModal
  } = useGuestPermission();

  // 处理点赞状态切换
  const handleLikeToggle = (postId, isLiked) => {
    console.log(`父组件收到点赞切换: postId=${postId}, isLiked=${isLiked}`);

    // 只更新点赞状态，不更新帖子数据（子组件自己管理）
    setLikedPosts(prev => {
      const newSet = new Set(prev);
      if (isLiked) {
        newSet.add(postId);
      } else {
        newSet.delete(postId);
      }
      return newSet;
    });
  };

  // 处理帖子点击
  const handlePostPress = (post) => {
    console.log('点击帖子:', post.id);
    router.push(`/Community/Post?id=${post.id}`);
  };

  const tabs = ['学习搭子', '自习室', '职通车', '互助站'];

  // 从URL参数中获取tab参数
  const tab = params.tab;
  const urlFilterTag = params.filterTag; // 从URL参数中获取筛选标签

  // 标签与页面映射
  const tabToPage = {
    '学习搭子': '/community-team',
    '自习室': '/community-study',
    '职通车': '/community-career',
    '互助站': '/community-friends',
  };

  // 所有帖子数据，每个帖子都有标签和时间戳
  const allPostsData = [
    // 互助站标签的帖子
    {
      id: 7,
      user: '团队队长',
      time: '三小时前',
      timestamp: Date.now() - 3 * 60 * 60 * 1000, // 3小时前
      content: '寻找学习伙伴！一起准备考研，互相监督',
      likes: 67,
      comments: 12,
      hasMedia: false,
      tag: '互助站'
    },
    {
      id: 8,
      user: '学习小组',
      time: '五小时前',
      timestamp: Date.now() - 5 * 60 * 60 * 1000, // 5小时前
      content: '英语口语练习小组招新，每周线上练习',
      likes: 123,
      comments: 31,
      hasMedia: true,
      mediaType: 'single',
      tag: '互助站'
    },
    {
      id: 9,
      user: '互助达人',
      time: '六小时前',
      timestamp: Date.now() - 6 * 60 * 60 * 1000, // 6小时前
      content: '组建数学学习小组，一起攻克高数难题！',
      likes: 89,
      comments: 15,
      hasMedia: false,
      tag: '互助站'
    },
    {
      id: 10,
      user: '小助手',
      time: '八小时前',
      timestamp: Date.now() - 8 * 60 * 60 * 1000, // 8小时前
      content: '分享一些学习资料，大家一起进步',
      likes: 156,
      comments: 28,
      hasMedia: true,
      mediaType: 'grid',
      tag: '互助站'
    },
    // 自习室标签的帖子
    {
      id: 3,
      user: '喵喵喵',
      time: '一小时前',
      timestamp: Date.now() - 1 * 60 * 60 * 1000, // 1小时前
      content: '今天在图书馆自习，效率很高！推荐大家也来试试',
      likes: 28,
      comments: 5,
      hasMedia: false,
      tag: '自习室'
    },
    {
      id: 4,
      user: '学习达人',
      time: '两小时前',
      timestamp: Date.now() - 2 * 60 * 60 * 1000, // 2小时前
      content: '分享一个高效学习方法：番茄工作法真的很有效',
      likes: 156,
      comments: 23,
      hasMedia: true,
      mediaType: 'single',
      tag: '自习室'
    },
    {
      id: 11,
      user: '自习小能手',
      time: '七小时前',
      timestamp: Date.now() - 7 * 60 * 60 * 1000, // 7小时前
      content: '今日自习打卡：完成了三章数学复习，感觉棒棒的！',
      likes: 45,
      comments: 8,
      hasMedia: false,
      tag: '自习室'
    },
    {
      id: 12,
      user: '图书馆常客',
      time: '九小时前',
      timestamp: Date.now() - 9 * 60 * 60 * 1000, // 9小时前
      content: '推荐几个安静的自习地点，环境超棒！',
      likes: 78,
      comments: 16,
      hasMedia: true,
      mediaType: 'grid',
      tag: '自习室'
    },
    // 职通车标签的帖子
    {
      id: 5,
      user: '职场导师',
      time: '四小时前',
      timestamp: Date.now() - 4 * 60 * 60 * 1000, // 4小时前
      content: '应届毕业生如何选择第一份工作？我的建议是...',
      likes: 89,
      comments: 18,
      hasMedia: false,
      tag: '职通车'
    },
    {
      id: 6,
      user: 'HR小姐姐',
      time: '昨天',
      timestamp: Date.now() - 24 * 60 * 60 * 1000, // 24小时前
      content: '面试技巧分享：如何让HR对你印象深刻',
      likes: 234,
      comments: 45,
      hasMedia: true,
      mediaType: 'grid',
      tag: '职通车'
    },
    {
      id: 13,
      user: '职场新人',
      time: '十小时前',
      timestamp: Date.now() - 10 * 60 * 60 * 1000, // 10小时前
      content: '刚入职场的感悟，分享给即将毕业的同学们',
      likes: 67,
      comments: 12,
      hasMedia: false,
      tag: '职通车'
    },
    {
      id: 14,
      user: '资深猎头',
      time: '十二小时前',
      timestamp: Date.now() - 12 * 60 * 60 * 1000, // 12小时前
      content: '2024年热门职业趋势分析，这些岗位值得关注',
      likes: 189,
      comments: 34,
      hasMedia: true,
      mediaType: 'single',
      tag: '职通车'
    },
    // 学习搭子标签的帖子
    {
      id: 1,
      user: 'Jessica',
      time: '刚刚',
      timestamp: Date.now(), // 当前时间
      content: '今天学习了新的编程技巧，感觉很有收获！',
      likes: 42,
      comments: 8,
      hasMedia: true,
      mediaType: 'single',
      tag: '学习搭子'
    },
    {
      id: 2,
      user: 'Kin',
      time: '30分钟前',
      timestamp: Date.now() - 30 * 60 * 1000, // 30分钟前
      content: '分享一些学习资源，希望对大家有帮助',
      likes: 156,
      comments: 23,
      hasMedia: true,
      mediaType: 'grid',
      tag: '学习搭子'
    },
    {
      id: 15,
      user: '编程小白',
      time: '十一小时前',
      timestamp: Date.now() - 11 * 60 * 60 * 1000, // 11小时前
      content: '求推荐适合初学者的编程学习路径，谢谢大家！',
      likes: 34,
      comments: 19,
      hasMedia: false,
      tag: '学习搭子'
    },
    {
      id: 16,
      user: '学霸小组长',
      time: '十三小时前',
      timestamp: Date.now() - 13 * 60 * 60 * 1000, // 13小时前
      content: '组建算法学习小组，每周刷题打卡，一起进步！',
      likes: 98,
      comments: 27,
      hasMedia: true,
      mediaType: 'single',
      tag: '学习搭子'
    }
  ];

  // 随机打乱数组的函数
  const shuffleArray = (array) => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  };

  // 按时间排序的函数（最新的在前面）
  const sortByTime = (array) => {
    return [...array].sort((a, b) => b.timestamp - a.timestamp);
  };

  // 获取帖子的函数
  const getPosts = (tag = null, sortByTimeOrder = false) => {
    let filteredPosts;

    if (tag) {
      // 如果有标签筛选，返回对应标签的帖子
      filteredPosts = allPostsData.filter(post => post.tag === tag);
    } else {
      // 如果没有标签筛选，返回所有帖子
      filteredPosts = [...allPostsData];
    }

    // 根据参数决定是否按时间排序
    if (sortByTimeOrder) {
      return sortByTime(filteredPosts);
    } else {
      // 如果不是显示全部，则随机排序（保持原有逻辑）
      return tag ? filteredPosts : shuffleArray(filteredPosts);
    }
  };

  // 初始化帖子
  useEffect(() => {
    if (urlFilterTag && tabs.includes(urlFilterTag)) {
      // 如果从其他页面返回并携带了筛选标签，显示对应标签的帖子
      setFilterTag(urlFilterTag);
      setPosts(getPosts(urlFilterTag));
    } else if (tab && tabs.includes(tab)) {
      // 如果从顶部按钮返回，显示对应标签的帖子
      setFilterTag(tab);
      setPosts(getPosts(tab));
    } else {
      // 首次进入，按时间排序显示所有帖子
      setFilterTag(null);
      setPosts(getPosts(null, true));
    }
  }, [tab, urlFilterTag]);

  // 清理函数，确保在组件卸载时清理焦点
  useEffect(() => {
    return () => {
      // 清理任何可能的焦点状态
      setSidebarVisible(false);
    };
  }, []);

  // 下拉刷新处理函数
  const onRefresh = () => {
    setRefreshing(true);
    // 模拟网络请求延迟
    setTimeout(() => {
      // 根据当前筛选状态重新获取帖子
      if (filterTag) {
        // 如果有筛选标签，显示对应标签的帖子
        setPosts(getPosts(filterTag));
      } else {
        // 如果没有筛选标签，按时间排序显示所有帖子
        setPosts(getPosts(null, true));
      }
      setRefreshing(false);
    }, 1000);
  };

  // 图片资源现在在PostCard组件中管理

  return (
    <ThemedView style={styles.container}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      {/* 固定顶部标题栏 */}
      <View style={styles.fixedHeader}>
        <TouchableOpacity
          style={styles.headerIcon}
          onPress={() => setSidebarVisible(true)}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="打开侧边栏"
        >
          <Image source={require('../../assets/FrameThree.png')} style={{ width: 24, height: 24, resizeMode: 'contain' }} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>社区</ThemedText>
        <TouchableOpacity
          style={styles.headerIcon}
          onPress={() => {
            console.log('Button clicked! Navigating to Chat page...');
            try {
              router.push('../Chat');
              console.log('Navigation attempted with ../Chat');
            } catch (error) {
              console.error('Navigation error:', error);
            }
          }}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="打开聊天页面"
        >
          <Image source={require('../../assets/FrameFour.png')} style={{ width: 24, height: 24, resizeMode: 'contain' }} />
        </TouchableOpacity>
      </View>

      {/* 侧边栏 */}
      <ProfileSidebar visible={sidebarVisible} onClose={() => setSidebarVisible(false)} />

      {/* 整体可滚动的瀑布流 */}
      <ScrollView
        style={styles.postsContainer}
        contentContainerStyle={{ paddingTop: 140, paddingBottom: 120 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#4A90E2']}
            tintColor="#4A90E2"
            title=""
            titleColor="transparent"
            progressBackgroundColor="transparent"
            progressViewOffset={160}
            style={{ backgroundColor: 'transparent' }}
          />
        }
      >
        {/* 四个tab按钮 */}
        <View style={styles.tabCardRow}>
          {tabs.map((tab, index) => {
            // 定义每张卡片的默认颜色
            const cardColors = ['#F1E2FF', '#FFDDB4', '#E2ECE1', '#DCECF9'];

            // 定义每张卡片选中时的颜色配置
            const getSelectedColors = (tabName) => {
              const colorConfig = {
                '学习搭子': { background: '#F1E2FF', button: '#B198C9' },
                '自习室': { background: '#FFDDB4', button: '#E59B62' },
                '职通车': { background: '#E2ECE1', button: '#9EC89A' },
                '互助站': { background: '#DCECF9', button: '#60A0D4' }
              };
              return colorConfig[tabName] || { background: cardColors[index], button: '#4A90E2' };
            };

            // 学习搭子和职通车位置稍高
            const isHigher = tab === '学习搭子' || tab === '职通车';
            const selectedColors = getSelectedColors(tab);

            return (
              <TouchableOpacity
                key={tab}
                style={[
                  styles.tabCardBox,
                  filterTag === tab && styles.tabCardBoxActive,
                  isHigher && styles.tabCardBoxHigher
                ]}
                onPress={() => {
                  if (tabToPage[tab]) {
                    router.push(tabToPage[tab]);
                  }
                }}
              >
                <LinearGradient
                  colors={filterTag === tab ?
                    [selectedColors.background, selectedColors.background] :
                    [cardColors[index], cardColors[index]]
                  }
                  style={styles.tabCardContent}
                >
                  <ThemedText style={[
                    styles.tabCardText,
                    filterTag === tab && { ...styles.tabCardTextActive, color: selectedColors.button }
                  ]}>
                    {tab}
                  </ThemedText>
                </LinearGradient>
              </TouchableOpacity>
            );
          })}
        </View>



        {/* 当前筛选标签显示 */}
        {filterTag && (
          <View style={[
            styles.filterIndicator,
            {
              backgroundColor: (() => {
                const backgroundColorConfig = {
                  '学习搭子': '#F1E2FF',
                  '自习室': '#FFDDB4',
                  '职通车': '#E2ECE1',
                  '互助站': '#DCECF9'
                };
                return backgroundColorConfig[filterTag] || '#F5F5F5';
              })()
            }
          ]}>
            <ThemedText style={[
              styles.filterText,
              {
                color: (() => {
                  const textColorConfig = {
                    '学习搭子': '#B198C9',
                    '自习室': '#E59B62',
                    '职通车': '#9EC89A',
                    '互助站': '#60A0D4'
                  };
                  return textColorConfig[filterTag] || '#60A0D4';
                })()
              }
            ]}>当前显示: {filterTag}</ThemedText>
            <TouchableOpacity
              style={[
                styles.clearFilter,
                {
                  backgroundColor: (() => {
                    const buttonColorConfig = {
                      '学习搭子': '#B198C9',
                      '自习室': '#E59B62',
                      '职通车': '#9EC89A',
                      '互助站': '#60A0D4'
                    };
                    return buttonColorConfig[filterTag] || '#60A0D4';
                  })()
                }
              ]}
              onPress={() => {
                setFilterTag(null);
                // 显示全部帖子，按时间排序（最新的在前面）
                setPosts(getPosts(null, true));
              }}
            >
              <ThemedText style={styles.clearFilterText}>显示全部</ThemedText>
            </TouchableOpacity>
          </View>
        )}

        {/* 显示全部帖子时的提示 */}
        {!filterTag && (
          <View style={styles.allPostsIndicator}>
            <ThemedText style={styles.allPostsText}>显示全部帖子（按时间排序）</ThemedText>
          </View>
        )}

        {/* 帖子卡片 */}
        {posts.map((post) => (
          <PostCard
            key={post.id}
            post={post}
            onPostPress={handlePostPress}
            onCommentPress={wrapGuestAction((post) => {
              console.log('评论帖子:', post.id);
            }, 'community-interaction')}
            onForwardPress={wrapGuestAction((post) => {
              console.log('转发帖子:', post.id);
            }, 'community-interaction')}
            onLikeToggle={handleLikeToggle}
          />
        ))}
      </ScrollView>

      {/* 游客权限提示弹窗 */}
      <GuestPermissionModal
        visible={showPermissionModal}
        onClose={closePermissionModal}
        onRegister={handleRegister}
        featureName={currentFeature.name}
        description={currentFeature.description}
      />
    </ThemedView>
  );
};

// 获取标签颜色的函数 - 已移除，因为标签已隐藏

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#FFF8F3', paddingHorizontal: 16 },
  safeArea: {
    height: 50,
    backgroundColor: '#FFF8F3',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  fixedHeader: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFF8F3',
    borderBottomWidth: 0,
    zIndex: 10,
    height: 60,
  },
  headerIcon: { padding: 8 },
  headerTitle: { fontSize: 18, fontWeight: 'bold', color: Colors.light.title },
  postsContainer: { flex: 1 },
  cardTabContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    marginTop: 0,
  },

  tabCardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    marginTop: 6, // 缩小距离顶部标题的距离
  },

  tabCardBox: {
    width: '23%', // 留出间隙使得一行刚好 4 个
    aspectRatio: 0.8, // 控制高度，例如 4:5 的比例
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    marginBottom: 16,
  },

  tabCardBoxActive: {
    elevation: 4,
  },

  tabCardBoxHigher: {
    transform: [{ translateY: -20 }], // 学习搭子和职通车位置稍高但高度不变
  },

  tabCardContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
  },

  tabCardText: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
  },

  tabCardTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },


  filterIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 16,
    // backgroundColor 现在动态设置
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
    // color 现在动态设置
  },
  clearFilter: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    // backgroundColor 现在动态设置
  },
  clearFilterText: {
    fontSize: 12,
    color: '#fff',
  },
  allPostsIndicator: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    marginBottom: 16,
    alignItems: 'center',
  },
  allPostsText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  post: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tagContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tagText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '500',
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  userInfo: { flexDirection: 'row', alignItems: 'center', flex: 1 },
  avatar: { width: 40, height: 40, borderRadius: 20, marginRight: 12 },
  userDetails: { flex: 1 },
  username: { fontSize: 14, fontWeight: 'bold', color: Colors.light.title },
  time: { fontSize: 12, color: Colors.light.iconColor, marginTop: 2 },
  postContent: { fontSize: 14, color: Colors.light.title, lineHeight: 20, marginBottom: 12 },
  mediaContainer: { marginBottom: 12 },
  singleMedia: { width: '100%', height: 200, borderRadius: 8, backgroundColor: '#f0f0f0' },
  gridMedia: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' },
  gridItem: { width: '30%', height: 80, borderRadius: 8, backgroundColor: '#f0f0f0', marginBottom: 8 },
  interactions: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  interactionBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  interactionBtnRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  interactionCount: { fontSize: 12, color: Colors.light.iconColor, marginLeft: 4 },

  // 点赞动画样式
  likeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  likeButton: {
    marginRight: 4,
  },
  likeIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.light.iconColor,
  },
  likeIconActive: {
    tintColor: '#FF69B4', // 粉红色
  },
  likeCount: {
    fontSize: 12,
    color: Colors.light.iconColor,
    marginLeft: 4,
  },
  likeCountActive: {
    color: '#FF69B4', // 粉红色
    fontWeight: 'bold',
  },
  particle: {
    position: 'absolute',
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FF69B4',
    left: 10, // 相对于点赞按钮的位置
    top: 10,
  },
});

export default Community;
