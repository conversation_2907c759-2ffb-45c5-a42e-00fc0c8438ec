import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, Image, TextInput, Alert, Animated, Easing, KeyboardAvoidingView, Platform } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import ThemedText from "../../components/ThemedText";
import ThemedView from "../../components/ThemedView";
import { Colors } from "../../constants/Colors";
import { useGuestPermission } from '../../hooks/useGuestPermission';
import GuestPermissionModal from '../../components/GuestPermissionModal';
import apiServices from '../../lib/apiServices';

// 导入图标
const likeIcon = require('../../assets/Like33.png');
const commentIcon = require('../../assets/Comments.png');
const forwardIcon = require('../../assets/Many.png');
const backIcon = require('../../assets/Arrows_left.png');

// 导入头像图片
const avatarJessica = require('../../assets/Community_image/AvatarOne.png');
const avatarKin = require('../../assets/Community_image/AvatarTwo.png');
const avatarCaaary = require('../../assets/Community_image/AvatarThree.png');

// 带动画的点赞按钮组件
const AnimatedLikeButton = ({ isLiked: initialLiked, likeCount: initialCount, onLikeToggle }) => {
  const [isLiked, setIsLiked] = useState(initialLiked);
  const [likeCount, setLikeCount] = useState(initialCount);
  const [isAnimating, setIsAnimating] = useState(false);

  // 动画值
  const shakeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const particleAnims = useRef(Array.from({ length: 6 }, () => ({
    translateX: new Animated.Value(0),
    translateY: new Animated.Value(0),
    opacity: new Animated.Value(0),
    scale: new Animated.Value(1)
  }))).current;

  // 点赞动画
  const animateLike = () => {
    if (isAnimating) return;
    setIsAnimating(true);

    // 抖动动画
    Animated.sequence([
      Animated.timing(shakeAnim, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: 5, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: 0, duration: 50, useNativeDriver: true })
    ]).start();

    // 缩放动画
    Animated.sequence([
      Animated.timing(scaleAnim, { toValue: 1.3, duration: 100, useNativeDriver: true }),
      Animated.timing(scaleAnim, { toValue: 1, duration: 100, useNativeDriver: true })
    ]).start();

    // 粒子散发动画
    const particleAnimations = particleAnims.map((particle, index) => {
      const angle = (index * 60) * Math.PI / 180;
      const distance = 30;
      const targetX = Math.cos(angle) * distance;
      const targetY = Math.sin(angle) * distance;

      return Animated.parallel([
        Animated.timing(particle.translateX, { toValue: targetX, duration: 400, easing: Easing.out(Easing.quad), useNativeDriver: true }),
        Animated.timing(particle.translateY, { toValue: targetY, duration: 400, easing: Easing.out(Easing.quad), useNativeDriver: true }),
        Animated.sequence([
          Animated.timing(particle.opacity, { toValue: 1, duration: 100, useNativeDriver: true }),
          Animated.timing(particle.opacity, { toValue: 0, duration: 300, useNativeDriver: true })
        ]),
        Animated.sequence([
          Animated.timing(particle.scale, { toValue: 1.2, duration: 100, useNativeDriver: true }),
          Animated.timing(particle.scale, { toValue: 0, duration: 300, useNativeDriver: true })
        ])
      ]);
    });

    Animated.parallel(particleAnimations).start(() => {
      particleAnims.forEach(particle => {
        particle.translateX.setValue(0);
        particle.translateY.setValue(0);
        particle.opacity.setValue(0);
        particle.scale.setValue(1);
      });
      setIsAnimating(false);
    });
  };

  // 处理点赞
  const handleLike = async () => {
    if (isAnimating) return;

    const originalIsLiked = isLiked;
    const originalLikeCount = likeCount;

    try {
      if (!isLiked) {
        setIsAnimating(true);
        animateLike();
        setIsLiked(true);
        setLikeCount(prev => prev + 1);
      } else {
        setIsLiked(false);
        setLikeCount(prev => prev - 1);
      }

      onLikeToggle && onLikeToggle(!originalIsLiked);

    } catch (error) {
      console.error('点赞操作失败:', error);
      setIsLiked(originalIsLiked);
      setLikeCount(originalLikeCount);
    } finally {
      setIsAnimating(false);
    }
  };

  return (
    <TouchableOpacity style={styles.likeContainer} onPress={handleLike}>
      {/* 粒子效果 */}
      {particleAnims.map((particle, index) => (
        <Animated.View
          key={index}
          style={[
            styles.particle,
            {
              transform: [
                { translateX: particle.translateX },
                { translateY: particle.translateY },
                { scale: particle.scale }
              ],
              opacity: particle.opacity
            }
          ]}
        />
      ))}

      {/* 点赞按钮 */}
      <Animated.View
        style={[
          styles.likeButton,
          {
            transform: [
              { translateX: shakeAnim },
              { scale: scaleAnim }
            ]
          }
        ]}
      >
        <Image
          source={likeIcon}
          style={[
            styles.likeIcon,
            isLiked && styles.likeIconActive
          ]}
        />
      </Animated.View>

      <ThemedText style={[
        styles.likeCount,
        isLiked && styles.likeCountActive
      ]}>
        {likeCount}
      </ThemedText>
    </TouchableOpacity>
  );
};

const Post = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [post, setPost] = useState(null);
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 权限管理
  const {
    wrapGuestAction,
    showPermissionModal,
    currentFeature,
    handleRegister,
    closePermissionModal
  } = useGuestPermission();

  // 模拟帖子数据
  useEffect(() => {
    // 这里应该根据params.id从API获取帖子详情
    const mockPost = {
      id: params.id || '1',
      user: 'Jessica',
      time: '2小时前',
      content: '今天在图书馆学习了一整天，感觉收获满满！分享一些学习心得：\n\n1. 制定明确的学习计划\n2. 保持专注，避免分心\n3. 定期休息，劳逸结合\n4. 及时复习，巩固知识\n\n希望对大家有帮助！一起加油💪',
      likes: 42,
      commentsCount: 8,
      hasMedia: true,
      mediaType: 'single'
    };

    const mockComments = [
      {
        id: '1',
        user: 'Kin',
        time: '1小时前',
        content: '说得很好！我也觉得制定计划很重要',
        likes: 5,
        isLiked: false
      },
      {
        id: '2',
        user: 'Caaary',
        time: '30分钟前',
        content: '谢谢分享，学到了！',
        likes: 3,
        isLiked: true
      },
      {
        id: '3',
        user: '学习小助手',
        time: '15分钟前',
        content: '非常实用的建议，特别是劳逸结合这一点👍',
        likes: 8,
        isLiked: false
      }
    ];

    setPost(mockPost);
    setComments(mockComments);
  }, [params.id]);

  // 获取用户头像
  const getUserAvatar = (user) => {
    switch (user) {
      case 'Jessica':
        return <Image source={avatarJessica} style={styles.avatar} />;
      case 'Kin':
        return <Image source={avatarKin} style={styles.avatar} />;
      case 'Caaary':
        return <Image source={avatarCaaary} style={styles.avatar} />;
      default:
        return <View style={[styles.avatar, { backgroundColor: '#ddd' }]} />;
    }
  };

  // 处理发布评论
  const handleSubmitComment = wrapGuestAction(async () => {
    if (!newComment.trim()) {
      Alert.alert('提示', '请输入评论内容');
      return;
    }

    setIsSubmitting(true);
    try {
      // 这里应该调用API发布评论
      // await apiServices.post.addComment(post.id, newComment);

      // 模拟添加评论
      const newCommentObj = {
        id: Date.now().toString(),
        user: '我',
        time: '刚刚',
        content: newComment.trim(),
        likes: 0,
        isLiked: false
      };

      setComments(prev => [newCommentObj, ...prev]);
      setNewComment('');
      Alert.alert('成功', '评论发布成功！');

    } catch (error) {
      console.error('发布评论失败:', error);
      Alert.alert('错误', '发布评论失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  }, 'community-interaction');

  // 处理评论点赞
  const handleCommentLike = wrapGuestAction((commentId) => {
    setComments(prev => prev.map(comment =>
      comment.id === commentId
        ? {
          ...comment,
          isLiked: !comment.isLiked,
          likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1
        }
        : comment
    ));
  }, 'community-interaction');

  if (!post) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.safeArea} />
        <View style={styles.fixedHeader}>
          <TouchableOpacity style={styles.headerIcon} onPress={() => router.back()}>
            <Image source={backIcon} style={{ width: 24, height: 24, resizeMode: 'contain' }} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>帖子详情</ThemedText>
          <View style={styles.headerIcon} />
        </View>
        <View style={styles.loadingContainer}>
          <ThemedText>加载中...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      {/* 固定顶部标题栏 */}
      <View style={styles.fixedHeader}>
        <TouchableOpacity
          style={styles.headerIcon}
          onPress={() => router.back()}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="返回"
        >
          <Image source={backIcon} style={{ width: 24, height: 24, resizeMode: 'contain' }} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>帖子详情</ThemedText>
        <TouchableOpacity
          style={styles.headerIcon}
          onPress={wrapGuestAction(() => {
            console.log('分享帖子:', post.id);
          }, 'community-interaction')}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="分享"
        >
          <Image source={forwardIcon} style={{ width: 24, height: 24, resizeMode: 'contain' }} />
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={{ paddingTop: 120, paddingBottom: 100 }}
          showsVerticalScrollIndicator={false}
        >
          {/* 帖子内容 */}
          <View style={styles.postContainer}>
            {/* 用户信息 */}
            <View style={styles.postHeader}>
              <View style={styles.userInfo}>
                {getUserAvatar(post.user)}
                <View style={styles.userDetails}>
                  <ThemedText style={styles.username}>{post.user}</ThemedText>
                  <ThemedText style={styles.time}>{post.time}</ThemedText>
                </View>
              </View>
            </View>

            {/* 帖子内容 */}
            <ThemedText style={styles.postContent}>{post.content}</ThemedText>

            {/* 媒体内容 */}
            {post.hasMedia && (
              <View style={styles.mediaContainer}>
                <Image
                  source={require('../../assets/Community_image/PhotosOne.png')}
                  style={styles.postImage}
                />
              </View>
            )}

            {/* 互动按钮 */}
            <View style={styles.interactions}>
              <AnimatedLikeButton
                isLiked={false}
                likeCount={post.likes}
                onLikeToggle={(liked) => {
                  console.log('帖子点赞状态:', liked);
                }}
              />

              <TouchableOpacity style={styles.interactionBtn}>
                <Image source={commentIcon} style={{ width: 20, height: 20, marginRight: 4 }} />
                <ThemedText style={styles.interactionCount}>{comments.length}</ThemedText>
              </TouchableOpacity>

              <View style={{ flex: 1 }} />

              <TouchableOpacity
                style={styles.interactionBtnRight}
                onPress={wrapGuestAction(() => {
                  console.log('转发帖子:', post.id);
                }, 'community-interaction')}
              >
                <Image source={forwardIcon} style={{ width: 20, height: 20 }} />
              </TouchableOpacity>
            </View>
          </View>

          {/* 评论区标题 */}
          <View style={styles.commentsHeader}>
            <ThemedText style={styles.commentsTitle}>评论 ({comments.length})</ThemedText>
          </View>

          {/* 评论列表 */}
          {comments.map((comment) => (
            <View key={comment.id} style={styles.commentItem}>
              <View style={styles.commentHeader}>
                {getUserAvatar(comment.user)}
                <View style={styles.commentUserInfo}>
                  <ThemedText style={styles.commentUsername}>{comment.user}</ThemedText>
                  <ThemedText style={styles.commentTime}>{comment.time}</ThemedText>
                </View>
              </View>

              <ThemedText style={styles.commentContent}>{comment.content}</ThemedText>

              <TouchableOpacity
                style={styles.commentLikeBtn}
                onPress={() => handleCommentLike(comment.id)}
              >
                <Image
                  source={likeIcon}
                  style={[
                    styles.commentLikeIcon,
                    comment.isLiked && styles.commentLikeIconActive
                  ]}
                />
                <ThemedText style={[
                  styles.commentLikeCount,
                  comment.isLiked && styles.commentLikeCountActive
                ]}>
                  {comment.likes}
                </ThemedText>
              </TouchableOpacity>
            </View>
          ))}
        </ScrollView>

        {/* 评论输入框 */}
        <View style={styles.commentInputContainer}>
          <TextInput
            style={styles.commentInput}
            placeholder="写下你的评论..."
            placeholderTextColor="#999"
            value={newComment}
            onChangeText={setNewComment}
            multiline
            maxLength={500}
          />
          <TouchableOpacity
            style={[
              styles.submitButton,
              (!newComment.trim() || isSubmitting) && styles.submitButtonDisabled
            ]}
            onPress={handleSubmitComment}
            disabled={!newComment.trim() || isSubmitting}
          >
            <ThemedText style={[
              styles.submitButtonText,
              (!newComment.trim() || isSubmitting) && styles.submitButtonTextDisabled
            ]}>
              {isSubmitting ? '发布中...' : '发布'}
            </ThemedText>
          </TouchableOpacity>
        </View>

        {/* 底部安全视图 */}
        <View style={styles.bottomSafeArea} />
      </KeyboardAvoidingView>

      {/* 游客权限提示弹窗 */}
      <GuestPermissionModal
        visible={showPermissionModal}
        onClose={closePermissionModal}
        onRegister={handleRegister}
        featureName={currentFeature.name}
        description={currentFeature.description}
      />
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3'
  },
  safeArea: {
    height: 50,
    backgroundColor: '#FFF8F3',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  fixedHeader: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFF8F3',
    paddingHorizontal: 16,
    borderBottomWidth: 0,
    zIndex: 10,
    height: 60,
  },
  headerIcon: {
    padding: 8
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.title
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  postContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  userDetails: {
    flex: 1,
  },
  username: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  time: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  postContent: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    marginBottom: 16,
  },
  mediaContainer: {
    marginBottom: 16,
  },
  postImage: {
    width: '100%',
    height: 250,
    borderRadius: 8,
    resizeMode: 'cover',
  },
  interactions: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  interactionBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  interactionBtnRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  interactionCount: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  // 点赞动画样式
  likeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  likeButton: {
    marginRight: 4,
  },
  likeIcon: {
    width: 20,
    height: 20,
    tintColor: '#666',
  },
  likeIconActive: {
    tintColor: '#FF69B4',
  },
  likeCount: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  likeCountActive: {
    color: '#FF69B4',
    fontWeight: 'bold',
  },
  particle: {
    position: 'absolute',
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FF69B4',
    left: 10,
    top: 10,
  },
  // 评论区样式
  commentsHeader: {
    paddingVertical: 16,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: 16,
  },
  commentsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  commentItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  commentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  commentUserInfo: {
    flex: 1,
  },
  commentUsername: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  commentTime: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  commentContent: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    marginBottom: 8,
  },
  commentLikeBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  commentLikeIcon: {
    width: 16,
    height: 16,
    tintColor: '#999',
    marginRight: 4,
  },
  commentLikeIconActive: {
    tintColor: '#FF69B4',
  },
  commentLikeCount: {
    fontSize: 12,
    color: '#999',
  },
  commentLikeCountActive: {
    color: '#FF69B4',
    fontWeight: 'bold',
  },
  // 评论输入框样式
  commentInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',

  },
  commentInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 6,
    fontSize: 14,
    color: '#333',
    backgroundColor: '#f8f8f8',
    maxHeight: 80,
    marginRight: 12,
  },
  submitButton: {
    backgroundColor: '#4A90E2',
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  submitButtonTextDisabled: {
    color: '#999',
  },
  // 底部安全视图
  bottomSafeArea: {
    height: 34,
    backgroundColor: '#FFFFFF',
  },
});

export default Post;