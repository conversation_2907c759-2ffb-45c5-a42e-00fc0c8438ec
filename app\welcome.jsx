import { StyleSheet, View, Image, Platform } from 'react-native'
import { router } from 'expo-router'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { Colors } from '../constants/Colors'
import ThemedView from '../components/ThemedView'
import ThemedText from '../components/ThemedText'
import Spacer from '../components/Spacer'
import GradientGlassButton from '../components/GradientGlassButton'
import WebCompatibleView from '../components/WebCompatibleView'

import apiServices from '../lib/apiServices'
import { useRecommendation } from '../contexts/UserContext'
import { useUser } from '../hooks/useUser'

const Welcome = () => {
  const handleLogin = () => {
    router.push('/(auth)/login')
  }

  const { setRecommendations } = useRecommendation();
  const { setUser } = useUser();

  const handleGuest = async () => {
    try {
      console.log('开始优化的游客登录流程');

      // 使用整合的游客登录API
      const result = await apiServices.auth.performGuestLogin(setUser, setRecommendations);

      if (result.success) {
        console.log('游客登录成功:', result.message);

        // 游客登录后直接跳转到index页面，不设置isFirstLogin标记
        console.log('游客登录，直接跳转到index页面');

        // 跳转到dashboard的index页面
        router.replace('/(dashboard)/?tab=index');
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      console.error('游客登录过程失败:', err.message);

      // 降级处理：使用基础游客登录
      try {
        console.log('尝试基础游客登录');
        const guestUser = await apiServices.auth.createGuestSession();
        setUser(guestUser);

        // 游客登录后直接跳转到index页面
        router.replace('/(dashboard)/?tab=index');
        console.log('基础游客登录成功，已跳转到index页面');
      } catch (fallbackErr) {
        console.error('基础游客登录也失败:', fallbackErr.message);
        // 最后的降级：显示错误信息但仍然尝试跳转
        alert('登录遇到问题，但您仍可以继续使用应用');
        router.replace('/(dashboard)/?tab=index');
      }
    }
  };

  return (
    <WebCompatibleView
      style={styles.container}
      fallbackStyle={styles.fallbackStyle}
    >
      <ThemedView style={styles.innerContainer} safe={true}>
        <Spacer height={Platform.OS === 'web' ? 80 : 100} />

        {/* 卡通形象 */}
        <View style={styles.characterContainer}>
          <Image
            source={require('../assets/standdog.png')}
            style={[styles.characterImage, { resizeMode: 'contain' }]}
          />
        </View>

        <Spacer height={Platform.OS === 'web' ? 40 : 60} />

        {/* APP名称 */}
        <ThemedText style={styles.appName}>
          APP名称
        </ThemedText>

        <Spacer height={Platform.OS === 'web' ? 80 : 120} />

        {/* 登录按钮 */}
        <GradientGlassButton
          title="登录"
          onPress={handleLogin}
          style={styles.loginButton}
        />

        <Spacer height={20} />

        {/* 游客按钮 */}
        <GradientGlassButton
          title="游客"
          onPress={handleGuest}
          style={styles.guestButton}
          gradientColors={['#FFE7CE', '#FDAA6C']}
          borderColor="rgba(255, 231, 206, 0.5)"
          blurBackgroundColor="rgba(255, 231, 206, 0.3)"
        />

        <Spacer height={60} />
      </ThemedView>
    </WebCompatibleView>
  )
}

export default Welcome

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  fallbackStyle: {
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  innerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Platform.OS === 'web' ? 40 : 20,
  },
  characterContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  characterImage: {
    width: Platform.OS === 'web' ? 180 : 200,
    height: Platform.OS === 'web' ? 180 : 200,
  },
  appName: {
    fontSize: Platform.OS === 'web' ? 28 : 32,
    fontWeight: 'bold',
    color: Colors.boldText,
    textAlign: 'center',
  },
  loginButton: {
    width: '80%',
    maxWidth: Platform.OS === 'web' ? 350 : 300,
    marginTop: 0,
    marginBottom: 0,
  },
  guestButton: {
    width: '80%',
    maxWidth: Platform.OS === 'web' ? 350 : 300,
    marginTop: 0,
    marginBottom: 0,
  },
})
