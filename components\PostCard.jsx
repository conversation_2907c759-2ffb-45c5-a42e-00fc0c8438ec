import React, { useState, useRef } from 'react';
import { StyleSheet, View, TouchableOpacity, Image, Animated, Easing } from 'react-native';
import ThemedText from "./ThemedText";
import apiServices from '../lib/apiServices';

// 导入头像图片
const avatarJessica = require('../assets/Community_image/AvatarOne.png');
const avatarKin = require('../assets/Community_image/AvatarTwo.png');
const avatarCaaary = require('../assets/Community_image/AvatarThree.png');

// 导入图标
const likeIcon = require('../assets/Like33.png');
const commentIcon = require('../assets/Comments.png');
const forwardIcon = require('../assets/Many.png');

// 导入Kin的图片
const kinPictures = [
  require('../assets/Community_image/PictureTwo.png'),
  require('../assets/Community_image/PictureThree.png'),
  require('../assets/Community_image/PictureFour.png'),
  require('../assets/Community_image/PictureFive.png'),
  require('../assets/Community_image/PictureSix.png'),
  require('../assets/Community_image/PhotosOne.png'),
];

// 带动画的点赞组件
const AnimatedLikeButton = ({ post, onLikeToggle }) => {
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(post.likes);
  const [isAnimating, setIsAnimating] = useState(false);

  // 动画值
  const shakeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const particleAnims = useRef(Array.from({ length: 6 }, () => ({
    translateX: new Animated.Value(0),
    translateY: new Animated.Value(0),
    opacity: new Animated.Value(0),
    scale: new Animated.Value(1)
  }))).current;

  // 点赞动画
  const animateLike = () => {
    if (isAnimating) return;
    setIsAnimating(true);

    // 抖动动画
    Animated.sequence([
      Animated.timing(shakeAnim, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: 5, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: 0, duration: 50, useNativeDriver: true })
    ]).start();

    // 缩放动画
    Animated.sequence([
      Animated.timing(scaleAnim, { toValue: 1.3, duration: 100, useNativeDriver: true }),
      Animated.timing(scaleAnim, { toValue: 1, duration: 100, useNativeDriver: true })
    ]).start();

    // 粒子散发动画
    const particleAnimations = particleAnims.map((particle, index) => {
      const angle = (index * 60) * Math.PI / 180;
      const distance = 30;
      const targetX = Math.cos(angle) * distance;
      const targetY = Math.sin(angle) * distance;

      return Animated.parallel([
        Animated.timing(particle.translateX, { toValue: targetX, duration: 400, easing: Easing.out(Easing.quad), useNativeDriver: true }),
        Animated.timing(particle.translateY, { toValue: targetY, duration: 400, easing: Easing.out(Easing.quad), useNativeDriver: true }),
        Animated.sequence([
          Animated.timing(particle.opacity, { toValue: 1, duration: 100, useNativeDriver: true }),
          Animated.timing(particle.opacity, { toValue: 0, duration: 300, useNativeDriver: true })
        ]),
        Animated.sequence([
          Animated.timing(particle.scale, { toValue: 1.2, duration: 100, useNativeDriver: true }),
          Animated.timing(particle.scale, { toValue: 0, duration: 300, useNativeDriver: true })
        ])
      ]);
    });

    Animated.parallel(particleAnimations).start(() => {
      particleAnims.forEach(particle => {
        particle.translateX.setValue(0);
        particle.translateY.setValue(0);
        particle.opacity.setValue(0);
        particle.scale.setValue(1);
      });
      setIsAnimating(false);
    });
  };

  // 处理点赞
  const handleLike = async () => {
    if (isAnimating) return;

    const originalIsLiked = isLiked;
    const originalLikeCount = likeCount;

    try {
      if (!isLiked) {
        setIsAnimating(true);
        animateLike();
        setIsLiked(true);
        setLikeCount(prev => prev + 1);

        await apiServices.post.like(post.id);
      } else {
        setIsLiked(false);
        setLikeCount(prev => prev - 1);

        await apiServices.post.unlike(post.id);
      }

      onLikeToggle && onLikeToggle(post.id, !originalIsLiked);

    } catch (error) {
      console.error('点赞操作失败:', error);
      setIsLiked(originalIsLiked);
      setLikeCount(originalLikeCount);
    } finally {
      setIsAnimating(false);
    }
  };

  return (
    <TouchableOpacity style={styles.likeContainer} onPress={handleLike}>
      {/* 粒子效果 */}
      {particleAnims.map((particle, index) => (
        <Animated.View
          key={index}
          style={[
            styles.particle,
            {
              transform: [
                { translateX: particle.translateX },
                { translateY: particle.translateY },
                { scale: particle.scale }
              ],
              opacity: particle.opacity
            }
          ]}
        />
      ))}

      {/* 点赞按钮 */}
      <Animated.View
        style={[
          styles.likeButton,
          {
            transform: [
              { translateX: shakeAnim },
              { scale: scaleAnim }
            ]
          }
        ]}
      >
        <Image
          source={likeIcon}
          style={[
            styles.likeIcon,
            isLiked && styles.likeIconActive
          ]}
        />
      </Animated.View>

      <ThemedText style={[
        styles.likeCount,
        isLiked && styles.likeCountActive
      ]}>
        {likeCount}
      </ThemedText>
    </TouchableOpacity>
  );
};

// 帖子卡片组件
const PostCard = ({ post, onCommentPress, onForwardPress, onLikeToggle, onPostPress }) => {
  // 获取用户头像
  const getUserAvatar = (user) => {
    switch (user) {
      case 'Jessica':
        return <Image source={avatarJessica} style={styles.avatar} />;
      case 'Kin':
        return <Image source={avatarKin} style={styles.avatar} />;
      case 'Caaary':
        return <Image source={avatarCaaary} style={styles.avatar} />;
      default:
        return <View style={[styles.avatar, { backgroundColor: '#ddd' }]} />;
    }
  };

  // 渲染媒体内容
  const renderMedia = () => {
    if (!post.hasMedia) return null;

    return (
      <View style={styles.mediaContainer}>
        {post.mediaType === 'single' ? (
          post.user === 'Jessica' ? (
            <Image source={require('../assets/Community_image/PhotosOne.png')} style={styles.singleMedia} />
          ) : (
            <View style={styles.singleMedia} />
          )
        ) : (
          post.user === 'Kin' ? (
            <View style={[styles.gridMedia, { flexDirection: 'row', flexWrap: 'wrap' }]}>
              {kinPictures.map((img, idx) => (
                <Image key={idx} source={img} style={{ width: 80, height: 80, borderRadius: 8, margin: 2 }} />
              ))}
            </View>
          ) : ['夜猫子', 'HR小姐姐', '数学小组'].includes(post.user) ? (
            <View style={[styles.gridMedia, { flexDirection: 'row', flexWrap: 'wrap' }]}>
              {kinPictures.slice(0, 4).map((img, idx) => (
                <Image key={idx} source={img} style={{ width: 80, height: 80, borderRadius: 8, margin: 2 }} />
              ))}
            </View>
          ) : (
            <View style={styles.gridMedia}>
              {[1, 2, 3, 4, 5, 6].map((item) => (
                <View key={item} style={styles.gridItem} />
              ))}
            </View>
          )
        )}
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={styles.post}
      onPress={() => onPostPress && onPostPress(post)}
      activeOpacity={0.95}
    >
      {/* 用户信息 */}
      <View style={styles.postHeader}>
        <View style={styles.userInfo}>
          {getUserAvatar(post.user)}
          <View style={styles.userDetails}>
            <ThemedText style={styles.username}>{post.user}</ThemedText>
            <ThemedText style={styles.time}>{post.time}</ThemedText>
          </View>
        </View>
      </View>

      {/* 帖子内容 */}
      <ThemedText style={styles.postContent}>{post.content}</ThemedText>

      {/* 媒体内容 */}
      {renderMedia()}

      {/* 互动按钮 */}
      <View style={styles.interactions}>
        {/* 点赞按钮 */}
        <View style={styles.interactionBtn}>
          <AnimatedLikeButton
            post={post}
            onLikeToggle={onLikeToggle}
          />
        </View>

        {/* 评论按钮 */}
        <TouchableOpacity
          style={styles.interactionBtn}
          onPress={() => onCommentPress && onCommentPress(post)}
        >
          <Image source={commentIcon} style={{ width: 20, height: 20, marginRight: 2 }} />
          <ThemedText style={styles.interactionCount}>{post.comments}</ThemedText>
        </TouchableOpacity>

        {/* 占位符 */}
        <View style={{ flex: 1 }} />

        {/* 转发按钮 */}
        <TouchableOpacity
          style={styles.interactionBtnRight}
          onPress={() => onForwardPress && onForwardPress(post)}
        >
          <Image source={forwardIcon} style={{ width: 20, height: 20 }} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  post: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  userDetails: {
    flex: 1,
  },
  username: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  time: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  postContent: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    marginBottom: 12,
  },
  mediaContainer: {
    marginBottom: 12,
  },
  singleMedia: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  gridMedia: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  gridItem: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    margin: 2,
  },
  interactions: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  interactionBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  interactionBtnRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  interactionCount: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  // 点赞动画样式
  likeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  likeButton: {
    marginRight: 4,
  },
  likeIcon: {
    width: 20,
    height: 20,
    tintColor: '#666',
  },
  likeIconActive: {
    tintColor: '#FF69B4',
  },
  likeCount: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  likeCountActive: {
    color: '#FF69B4',
    fontWeight: 'bold',
  },
  particle: {
    position: 'absolute',
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FF69B4',
    left: 10,
    top: 10,
  },
});

export default PostCard;
