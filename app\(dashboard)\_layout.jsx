import { useLocal<PERSON><PERSON>ch<PERSON><PERSON><PERSON>, useRouter } from "expo-router"
import { useColorScheme, View, TouchableOpacity, Image, StyleSheet, Dimensions } from "react-native"
import { useEffect, useState, useRef, useCallback, memo } from "react"
import { Colors } from "../../constants/Colors"
import { useUser } from "../../hooks/useUser"
import AsyncStorage from '@react-native-async-storage/async-storage'
import { shadowPresets } from "../../utils/shadowUtils"
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing
} from 'react-native-reanimated'

import UserOnly from "../../components/auth/UserOnly"
import GuestPermissionModal from "../../components/GuestPermissionModal"
import { useGuestPermission } from "../../hooks/useGuestPermission"
import ThemedText from "../../components/ThemedText"

// 导入页面组件
import IndexPage from "./index"
import PlansPage from "./plans"
import CommunityPage from "./community"
import ProfilePage from "./profile"

const { width: screenWidth } = Dimensions.get('window')

// 定义颜色主题
const tabColors = {
  index: { bg: '#e3f2fd', slider: '#CDEAFA' },
  plans: { bg: '#e8f5e9', slider: '#E2DCF1' },
  more: { bg: '#fff3e0', slider: '#FF8D00' },
  community: { bg: '#f3e5f5', slider: '#DBF1CD' },
  profile: { bg: '#ffebee', slider: '#FFE2CD' }
};

export default function DashboardLayout() {
  const colorScheme = useColorScheme()
  const theme = Colors[colorScheme] ?? Colors.light
  const { tab } = useLocalSearchParams()
  const { user, authChecked, setAuthChecked } = useUser()
  const router = useRouter()
  const [ready, setReady] = useState(false)
  const [activeTab, setActiveTab] = useState('index')
  const [showMorePanel, setShowMorePanel] = useState(false)
  const [isAnimationReady, setIsAnimationReady] = useState(false)

  // 游客权限管理
  const {
    tryAccessPage,
    showPermissionModal,
    currentFeature,
    handleRegister,
    closePermissionModal
  } = useGuestPermission()

  // 动画值
  const tabBarContentWidth = screenWidth - 40;
  const actualTabWidth = tabBarContentWidth / 5;
  // 简化初始化：先设为0，后续通过useEffect设置正确位置
  const sliderTranslateX = useSharedValue(0)
  const sliderScale = useSharedValue(1)
  const sliderColor = useSharedValue(tabColors.index.slider)
  const iconScales = useRef([
    useSharedValue(1),
    useSharedValue(1),
    useSharedValue(1),
    useSharedValue(1),
    useSharedValue(1)
  ]).current

  // 为每个图标创建动画样式
  const iconAnimatedStyles = [
    useAnimatedStyle(() => ({
      transform: [{ scale: iconScales[0].value }],
    })),
    useAnimatedStyle(() => ({
      transform: [{ scale: iconScales[1].value }],
    })),
    useAnimatedStyle(() => ({
      transform: [{ scale: iconScales[2].value }],
    })),
    useAnimatedStyle(() => ({
      transform: [{ scale: iconScales[3].value }],
    })),
    useAnimatedStyle(() => ({
      transform: [{ scale: iconScales[4].value }],
    })),
  ];

  // 优化的图标未选中状态动画 - 流畅的反向旋转
  const iconInactiveStyles = [
    useAnimatedStyle(() => ({
      opacity: isAnimationReady ? withTiming(activeTab === 'index' ? 0 : 1, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1)
      }) : (activeTab === 'index' ? 0 : 1),
      transform: [
        {
          rotate: isAnimationReady ? withTiming(activeTab === 'index' ? '-180deg' : '0deg', {
            duration: 300,
            easing: Easing.bezier(0.25, 0.1, 0.25, 1)
          }) : (activeTab === 'index' ? '-180deg' : '0deg')
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(activeTab === 'plans' ? 0 : 1, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1)
      }),
      transform: [
        {
          rotate: withTiming(activeTab === 'plans' ? '-180deg' : '0deg', {
            duration: 300,
            easing: Easing.bezier(0.25, 0.1, 0.25, 1)
          })
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(showMorePanel ? 0 : 1, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1)
      }),
      transform: [
        {
          rotate: withTiming(showMorePanel ? '-180deg' : '0deg', {
            duration: 300,
            easing: Easing.bezier(0.25, 0.1, 0.25, 1)
          })
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(activeTab === 'community' ? 0 : 1, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1)
      }),
      transform: [
        {
          rotate: withTiming(activeTab === 'community' ? '-180deg' : '0deg', {
            duration: 300,
            easing: Easing.bezier(0.25, 0.1, 0.25, 1)
          })
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(activeTab === 'profile' ? 0 : 1, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1)
      }),
      transform: [
        {
          rotate: withTiming(activeTab === 'profile' ? '-180deg' : '0deg', {
            duration: 300,
            easing: Easing.bezier(0.25, 0.1, 0.25, 1)
          })
        }
      ]
    })),
  ];

  // 优化的图标选中状态动画 - 流畅的旋转效果
  const iconActiveStyles = [
    useAnimatedStyle(() => ({
      opacity: withTiming(activeTab === 'index' ? 1 : 0, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1)
      }),
      transform: [
        {
          rotate: withTiming(activeTab === 'index' ? '0deg' : '180deg', {
            duration: 300,
            easing: Easing.bezier(0.25, 0.1, 0.25, 1)
          })
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(activeTab === 'plans' ? 1 : 0, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1)
      }),
      transform: [
        {
          rotate: withTiming(activeTab === 'plans' ? '0deg' : '180deg', {
            duration: 300,
            easing: Easing.bezier(0.25, 0.1, 0.25, 1)
          })
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(showMorePanel ? 1 : 0, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1)
      }),
      transform: [
        {
          rotate: withTiming(showMorePanel ? '0deg' : '180deg', {
            duration: 300,
            easing: Easing.bezier(0.25, 0.1, 0.25, 1)
          })
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(activeTab === 'community' ? 1 : 0, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1)
      }),
      transform: [
        {
          rotate: withTiming(activeTab === 'community' ? '0deg' : '180deg', {
            duration: 300,
            easing: Easing.bezier(0.25, 0.1, 0.25, 1)
          })
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(activeTab === 'profile' ? 1 : 0, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1)
      }),
      transform: [
        {
          rotate: withTiming(activeTab === 'profile' ? '0deg' : '180deg', {
            duration: 300,
            easing: Easing.bezier(0.25, 0.1, 0.25, 1)
          })
        }
      ]
    })),
  ];

  // more 面板的动画样式
  const morePanelAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(showMorePanel ? 1 : 0, { duration: 250 }),
      transform: [
        {
          translateY: withTiming(showMorePanel ? 0 : 10, {
            duration: 250
          })
        },
        {
          scale: withTiming(showMorePanel ? 1 : 0.98, {
            duration: 300
          })
        }
      ]
    };
  });



  // 智能设置滑块位置的函数（支持动画和立即设置）
  const setSliderPosition = useCallback((tabName, immediate = false) => {
    const tabIndexMap = { 'index': 0, 'plans': 1, 'more': 2, 'community': 3, 'profile': 4 };
    const tabIndex = tabIndexMap[tabName] || 0;
    const tabBarContentWidth = screenWidth - 40;
    const actualTabWidth = tabBarContentWidth / 5;
    const targetPosition = actualTabWidth * tabIndex;

    if (immediate) {
      // 立即设置，不使用动画（用于初始化）
      sliderTranslateX.value = targetPosition;
      sliderColor.value = tabColors[tabName]?.slider || tabColors.index.slider;
    } else {
      // 使用平滑的动画配置
      sliderTranslateX.value = withTiming(targetPosition, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1) // 更平滑的贝塞尔曲线
      });
      sliderColor.value = withTiming(tabColors[tabName]?.slider || tabColors.index.slider, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1)
      });
    }
  }, [sliderTranslateX, sliderColor, screenWidth]);

  // 监听activeTab变化，同步更新滑块位置（仅在初始化时使用）
  useEffect(() => {
    if (ready && activeTab && !user) {
      // 只在用户状态未确定时使用，避免与用户交互冲突
      console.log('初始化时activeTab变化，更新滑块位置:', activeTab);
      setSliderPosition(activeTab, true); // 使用immediate=true避免初始化动画
    }
  }, [activeTab, ready, user, setSliderPosition]);

  // 用户状态确定时的初始化（只在首次加载时执行）
  // 注释掉这个逻辑，因为它会覆盖checkAndRedirect中设置的targetTab
  // useEffect(() => {
  //   if (user && !ready) {
  //     // 默认都从index开始，不再根据用户类型设置不同的初始tab
  //     const initialTab = 'index';
  //     console.log('首次用户状态确定，初始化滑块到:', initialTab);

  //     // 计算滑块位置
  //     const tabIndexMap = { 'index': 0, 'plans': 1, 'more': 2, 'community': 3, 'profile': 4 };
  //     const tabIndex = tabIndexMap[initialTab] || 0;
  //     const tabBarContentWidth = screenWidth - 40;
  //     const actualTabWidth = tabBarContentWidth / 5;
  //     const targetPosition = actualTabWidth * tabIndex;

  //     // 立即设置activeTab和滑块位置
  //     setActiveTab(initialTab);
  //     sliderTranslateX.value = targetPosition;
  //     sliderColor.value = tabColors[initialTab]?.slider || tabColors.index.slider;

  //     console.log('初始滑块位置已设置:', { initialTab, tabIndex, targetPosition });
  //   }
  // }, [user, ready, sliderTranslateX, sliderColor, screenWidth]);



  useEffect(() => {
    const checkAndRedirect = async () => {
      console.log('🔍 checkAndRedirect 开始执行:', {
        authChecked,
        user: !!user,
        userAccount: user?.userAccount,
        isGuest: user?.isGuest,
        ready
      });

      // 只在初始化时执行，避免用户操作时的重复执行
      if (authChecked && user && !ready) {
        console.log('✅ 满足checkAndRedirect条件，开始执行初始化逻辑');
        console.log('✅ Dashboard layout check:', {
          user: user.isGuest ? 'guest' : 'user',
          tab,
          activeTab,
          currentURL: typeof window !== 'undefined' ? window.location.href : 'N/A'
        });

        // 检查是否是页面刷新（URL中包含tab参数或路径参数）
        const currentUrl = typeof window !== 'undefined' ? window.location.href : '';
        const pathname = typeof window !== 'undefined' ? window.location.pathname : '';
        const search = typeof window !== 'undefined' ? window.location.search : '';

        // 尝试从URL参数中获取tab
        const urlTabMatch = currentUrl.match(/[?&]tab=(\w+)/);
        let urlTab = urlTabMatch ? urlTabMatch[1] : null;

        // 如果URL参数中没有tab，尝试从路径中获取
        if (!urlTab) {
          const pathMatch = pathname.match(/\/(dashboard\/)?(\w+)$/);
          if (pathMatch && pathMatch[2] && pathMatch[2] !== 'dashboard') {
            urlTab = pathMatch[2];
          }
        }

        console.log('🔍 URL解析详情:', {
          currentUrl,
          pathname,
          search,
          urlTab,
          componentTab: tab,
          urlTabMatch,
          pathMatch: pathname.match(/\/(dashboard\/)?(\w+)$/)
        });

        // 检查是否为游客用户
        console.log('🔍 用户类型检查:', {
          isGuest: user.isGuest,
          userAccount: user.userAccount,
          userName: user.userName,
          userRole: user.userRole,
          fullUser: user
        });

        if (user.isGuest) {
          console.log('🎭 检测到游客用户，应用游客逻辑');
          // 游客用户允许访问的页面
          const allowedTabs = ['index', 'community', 'profile'];

          // 优先使用URL中的tab参数（刷新时保持当前页面）
          let targetTab = null;
          if (urlTab && allowedTabs.includes(urlTab)) {
            targetTab = urlTab;
            console.log('Dashboard: 游客刷新，使用URL中的tab:', targetTab);
          } else if (tab && allowedTabs.includes(tab)) {
            targetTab = tab;
            console.log('Dashboard: 游客有有效的tab参数:', targetTab);
          } else {
            // 游客没有URL参数，尝试恢复上次访问的页面
            const lastRoute = await AsyncStorage.getItem('lastVisitedRoute');
            if (lastRoute && lastRoute.includes('tab=')) {
              const tabMatch = lastRoute.match(/tab=(\w+)/);
              const lastTab = tabMatch ? tabMatch[1] : 'index';

              if (allowedTabs.includes(lastTab)) {
                targetTab = lastTab;
                console.log('Dashboard: 游客恢复到上次访问的页面:', targetTab);
              }
            }

            // 如果还没有确定目标tab，使用默认的index页面
            if (!targetTab) {
              targetTab = 'index';
              console.log('Dashboard: 游客使用默认页面:', targetTab);
            }
          }

          // 如果访问受限页面，重定向到index
          if (!allowedTabs.includes(targetTab)) {
            console.log('游客用户访问受限tab:', targetTab, '重定向到index');
            targetTab = 'index';
          }

          // 设置目标tab
          setActiveTab(targetTab);
          setSliderPosition(targetTab, true);

          // 更新URL参数（如果需要）
          if (tab !== targetTab) {
            router.setParams({ tab: targetTab });
          }

          // 保存当前路由
          const currentRoute = `/(dashboard)/?tab=${targetTab}`;
          AsyncStorage.setItem('lastVisitedRoute', currentRoute);

          console.log('🎯 游客用户设置ready为true，targetTab:', targetTab);
          setReady(true);
          // 延迟启用动画，确保初始状态已正确设置
          setTimeout(() => setIsAnimationReady(true), 100);
          return;
        }

        // 正常用户的逻辑
        console.log('👤 检测到正常用户，应用正常用户逻辑');

        // 所有有效的tab页面
        const validTabs = ["plans", "index", "community", "profile"];

        // 优先使用URL中的tab参数（刷新时保持当前页面）
        let targetTab = null;
        if (urlTab && validTabs.includes(urlTab)) {
          targetTab = urlTab;
          console.log('🎯 Dashboard: 用户刷新，使用URL中的tab:', targetTab);
        } else if (tab && validTabs.includes(tab)) {
          targetTab = tab;
          console.log('🎯 Dashboard: 用户有有效的tab参数:', targetTab);
        } else {
          // 正常用户没有URL参数，尝试恢复上次访问的页面
          const lastRoute = await AsyncStorage.getItem('lastVisitedRoute');
          if (lastRoute && lastRoute.includes('tab=')) {
            const tabMatch = lastRoute.match(/tab=(\w+)/);
            const lastTab = tabMatch ? tabMatch[1] : 'plans';

            if (validTabs.includes(lastTab)) {
              targetTab = lastTab;
              console.log('Dashboard: 正常用户恢复到上次访问的页面:', targetTab);
            }
          }

          // 如果还没有确定目标tab，使用默认的plans页面
          if (!targetTab) {
            targetTab = 'plans';
            console.log('Dashboard: 正常用户使用默认页面plans');
          }
        }

        // 设置目标tab
        console.log('🔧 最终确定的targetTab:', targetTab, '当前activeTab:', activeTab);
        setActiveTab(targetTab);
        setSliderPosition(targetTab, true);

        // 更新URL参数（如果需要）
        if (tab !== targetTab) {
          console.log('🔄 更新URL参数从', tab, '到', targetTab);
          router.setParams({ tab: targetTab });
        }

        // 保存当前路由
        const currentRoute = targetTab === 'index' ? '/(dashboard)/?tab=index' : `/(dashboard)/${targetTab}?tab=${targetTab}`;
        AsyncStorage.setItem('lastVisitedRoute', currentRoute);

        console.log('🎯 正式用户设置ready为true，targetTab:', targetTab);
        setReady(true);
        // 延迟启用动画，确保初始状态已正确设置
        setTimeout(() => setIsAnimationReady(true), 100);
      } else {
        console.log('❌ checkAndRedirect 条件不满足:', {
          authChecked,
          user: !!user,
          userAccount: user?.userAccount,
          ready,
          condition1: authChecked && user,
          condition2: !ready,
          fullCondition: authChecked && user && !ready
        });
      }
    };
    checkAndRedirect();
  }, [authChecked, user, tab, router, setSliderPosition]);

  // Fallback: 确保ready状态最终被设置为true
  useEffect(() => {
    if (user && !ready) {
      console.log('⚠️ Fallback: 检测到用户但ready为false，启动修复逻辑');
      setTimeout(() => {
        if (!ready) {
          console.log('⚠️ Fallback执行: 设置ready为true，当前activeTab:', activeTab);
          setReady(true);
        }
        // 如果authChecked还是false，也强制设置为true（紧急修复）
        if (!authChecked) {
          console.log('🚨 紧急修复: authChecked仍为false，强制设置为true');
          setAuthChecked(true);
        }
      }, 1000); // 1秒后如果还没有ready，强制设置
    }
  }, [authChecked, user, ready, activeTab, setSliderPosition, setAuthChecked]);

  // 紧急修复：如果有用户但authChecked为false，立即修复
  useEffect(() => {
    if (user && !authChecked) {
      console.log('🚨 立即修复: 检测到用户但authChecked为false，立即设置为true');
      setAuthChecked(true);
    }
  }, [user, authChecked, setAuthChecked]);

  // 持续监控：确保authChecked状态不会被意外重置
  useEffect(() => {
    if (user && authChecked) {
      const interval = setInterval(() => {
        if (user && !authChecked) {
          console.log('🔄 持续监控: authChecked被重置，重新设置为true');
          setAuthChecked(true);
        }
      }, 500); // 每500ms检查一次

      return () => clearInterval(interval);
    }
  }, [user, authChecked, setAuthChecked]);

  // handleTabPress逻辑 - 带权限检查版本
  const handleTabPress = useCallback((tabName) => {
    console.log('🔥 Tab点击:', tabName, '当前:', activeTab);
    console.log('🔍 当前状态检查:', {
      authChecked,
      user: !!user,
      userAccount: user?.userAccount,
      ready,
      isGuest: user?.isGuest
    });

    // 简化状态检查：只要有用户对象就允许点击
    // authChecked状态不稳定，不作为阻止条件
    if (!user) {
      console.log('❌ 没有用户对象，忽略点击', { authChecked, user: !!user });
      return;
    }

    // 如果authChecked为false但有用户，立即修复
    if (!authChecked) {
      console.log('🔧 Tab点击时修复authChecked状态');
      setAuthChecked(true);
    }

    // 游客权限检查
    if (user?.isGuest && tabName === 'plans') {
      console.log('游客尝试访问plans页面，显示权限提示');
      tryAccessPage('plans');
      return;
    }

    if (tabName === 'more') {
      if (!showMorePanel) {
        setTimeout(() => setShowMorePanel(true), 300);
      } else {
        setShowMorePanel(false);
      }

      // 使用统一的滑块位置设置函数，确保动画一致性
      setSliderPosition('more', false);

      // 计算tabIndex用于图标缩放动画
      const tabIndexMap = { 'index': 0, 'plans': 1, 'more': 2, 'community': 3, 'profile': 4 };
      const tabIndex = tabIndexMap['more'];

      // more 图标的缩放动画 - 与其他tab保持一致
      iconScales.forEach((scale, index) => {
        if (index === tabIndex) {
          scale.value = withTiming(1.08, {
            duration: 300,
            easing: Easing.bezier(0.25, 0.1, 0.25, 1)
          });
        } else {
          scale.value = withTiming(1, {
            duration: 300,
            easing: Easing.bezier(0.25, 0.1, 0.25, 1)
          });
        }
      });

      return;
    }

    if (showMorePanel) setShowMorePanel(false);

    console.log('Tab点击处理:', tabName, '当前activeTab:', activeTab);
    console.log('当前URL tab参数:', tab);

    // 计算tabIndex用于图标动画
    const tabIndexMap = { 'index': 0, 'plans': 1, 'more': 2, 'community': 3, 'profile': 4 };
    const tabIndex = tabIndexMap[tabName] || 0;

    // 设置新的activeTab
    setActiveTab(tabName);
    console.log('设置新的activeTab为:', tabName);

    // 使用统一的滑块位置设置函数（带动画）
    setSliderPosition(tabName, false);

    // 优化图标缩放动画 - 使用平滑的动画配置
    iconScales.forEach((scale, index) => {
      if (index === tabIndex) {
        // 轻微的缩放效果，使用平滑动画
        scale.value = withTiming(1.08, {
          duration: 300,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1)
        });
      } else {
        scale.value = withTiming(1, {
          duration: 300,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1)
        });
      }
    });

    // 立即更新URL参数 - 使用浏览器API直接更新
    console.log('🔄 更新URL参数为:', tabName);

    // 尝试多种方法更新URL
    try {
      // 方法1: 使用expo-router的setParams
      router.setParams({ tab: tabName });

      // 方法2: 直接操作浏览器URL（Web平台）
      if (typeof window !== 'undefined' && window.history) {
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set('tab', tabName);
        window.history.replaceState({}, '', currentUrl.toString());
        console.log('🌐 直接更新浏览器URL为:', currentUrl.toString());
      }
    } catch (error) {
      console.log('⚠️ URL更新失败:', error.message);
    }

    // 验证URL是否更新成功
    setTimeout(() => {
      const currentUrl = typeof window !== 'undefined' ? window.location.href : '';
      console.log('🔍 URL更新后的地址:', currentUrl);
    }, 100);

    // 保存当前路由，用于刷新或重新登录时恢复
    const currentRoute = tabName === 'index' ? '/(dashboard)/?tab=index' : `/(dashboard)/${tabName}?tab=${tabName}`;
    console.log('💾 保存路由到AsyncStorage:', currentRoute);
    AsyncStorage.setItem('lastVisitedRoute', currentRoute);
  }, [router, iconScales, activeTab, sliderTranslateX, sliderColor, screenWidth, showMorePanel,
    setShowMorePanel, user, tryAccessPage, setSliderPosition]);

  const sliderAnimatedStyle = useAnimatedStyle(() => {
    const tabBarContentWidth = screenWidth - 40;
    const actualTabWidth = tabBarContentWidth / 5;
    const sliderSize = 60;
    // 让滑块居中于当前tab
    const centerX = sliderTranslateX.value + actualTabWidth / 2 - sliderSize / 2;
    return {
      transform: [
        { translateX: centerX },
        { scale: sliderScale.value }
      ],
      backgroundColor: sliderColor.value,
      width: sliderSize,
      height: sliderSize,
      borderRadius: sliderSize / 2,
      position: 'absolute',
      top: 15,
      zIndex: 0,
      alignSelf: 'flex-start',
    };
  });

  const tabItems = [
    {
      name: 'index',
      title: '主页',
      icon: require('../../assets/Tabbar/Index_Study_0.png'),
      iconActive: require('../../assets/Tabbar/Index_study_1.png')
    },
    {
      name: 'plans',
      title: '计划',
      icon: require('../../assets/Tabbar/Plan_crown.png'),
      iconActive: require('../../assets/Tabbar/Plan_crown_1.png')
    },
    {
      name: 'more',
      title: '更多',
      icon: require('../../assets/Tabbar/More_0.png'),
      iconActive: require('../../assets/Tabbar/More_1.png')
    },
    {
      name: 'community',
      title: '社区',
      icon: require('../../assets/Tabbar/Community_dog_scratch_0.png'),
      iconActive: require('../../assets/Tabbar/Community_dog_scratch_1.png')
    },
    {
      name: 'profile',
      title: '我的',
      icon: require('../../assets/Tabbar/Mine_smiling_0.png'),
      iconActive: require('../../assets/Tabbar/Mine_smiling_1.png')
    }
  ];

  if (!ready) {
    return null;
  }

  // 渲染所有页面，通过样式控制显示/隐藏，避免组件重新挂载
  const renderAllPages = () => {
    return (
      <>
        <View style={[styles.pageContainer, { display: activeTab === 'index' ? 'flex' : 'none' }]}>
          <IndexPage />
        </View>
        <View style={[styles.pageContainer, { display: activeTab === 'plans' ? 'flex' : 'none' }]}>
          <PlansPage />
        </View>
        <View style={[styles.pageContainer, { display: activeTab === 'community' ? 'flex' : 'none' }]}>
          <CommunityPage />
        </View>
        <View style={[styles.pageContainer, { display: activeTab === 'profile' ? 'flex' : 'none' }]}>
          <ProfilePage />
        </View>
      </>
    );
  };

  return (
    <UserOnly>
      <View style={styles.container}>
        {/* 页面内容 */}
        <View style={styles.content}>
          {renderAllPages()}
        </View>

        {/* 标签栏 */}
        <View style={[styles.tabBar, { height: showMorePanel ? 150 : 90 }]}>
          {/* More面板 - 三个横向盒子 */}
          {showMorePanel && (
            <Animated.View style={[styles.morePanelRow, morePanelAnimatedStyle]}>
              <TouchableOpacity
                style={styles.morePanelItem}
                onPress={() => {
                  setShowMorePanel(false);
                  router.push('/more_posting');
                }}
              >
                <ThemedText style={styles.morePanelText}>发帖</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.morePanelItem}
                onPress={() => {
                  setShowMorePanel(false);
                  router.push('/more_qa');
                }}
              >
                <ThemedText style={styles.morePanelText}>答疑</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.morePanelItem}
                onPress={() => {
                  setShowMorePanel(false);
                  router.push('/more_learning');
                }}
              >
                <ThemedText style={styles.morePanelText}>学习</ThemedText>
              </TouchableOpacity>
            </Animated.View>
          )}

          <View style={styles.tabRow}>
            <Animated.View style={[styles.slider, sliderAnimatedStyle]} />

            {tabItems.map((item, index) => (
              <TouchableOpacity
                key={item.name}
                style={styles.tabButton}
                onPress={() => handleTabPress(item.name)}
                activeOpacity={0.7}
              >
                <View style={styles.tabInnerWrap}>
                  <Animated.View style={iconAnimatedStyles[index]}>
                    {/* 图标容器 - 确保正确的层叠和清理 */}
                    <View style={styles.iconContainer}>
                      {/* 未选中状态的图标 */}
                      <Animated.Image
                        source={item.icon}
                        style={[
                          styles.tabIcon,
                          iconInactiveStyles[index]
                        ]}
                        resizeMode="contain"
                      />
                      {/* 选中状态的图标 */}
                      <Animated.Image
                        source={item.iconActive}
                        style={[
                          styles.tabIcon,
                          styles.tabIconActive,
                          iconActiveStyles[index]
                        ]}
                        resizeMode="contain"
                      />
                    </View>
                  </Animated.View>
                  <ThemedText style={styles.tabLabel}>{item.title}</ThemedText>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      {/* 游客权限提示弹窗 */}
      <GuestPermissionModal
        visible={showPermissionModal}
        onClose={closePermissionModal}
        onRegister={handleRegister}
        featureName={currentFeature.name}
        description={currentFeature.description}
      />
    </UserOnly>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#transparent',
  },
  content: {
    flex: 1,
    backgroundColor: Colors.background, // 使用Colors.js中的背景色
    // 移除固定的 marginBottom，让内容能够延伸到底部
  },
  pageContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  tabBar: {
    flexDirection: 'column',
    paddingBottom: 20, // tabbar下方的20padding
    position: 'absolute',
    bottom: 20, // 底部20间距，让页面内容可见
    left: 20,
    right: 20,
    borderRadius: 55,
    backgroundColor: Colors.button, // 使用Colors.js中的按钮颜色
    ...shadowPresets.heavy,
    zIndex: 1000, // 确保tabbar层级最高
  },
  morePanelRow: {
    flexDirection: 'row',
    height: 50,
    paddingHorizontal: 20,
    paddingVertical: 8,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  tabRow: {
    flexDirection: 'row',
    height: 90,
  },
  slider: {
    position: 'absolute',
    top: 15,
    left: 0,
    width: 60,
    height: 60,
    borderRadius: 30,
    zIndex: 0,
    alignSelf: 'center',
  },
  tabButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: 90,
    zIndex: 1,
  },
  tabInnerWrap: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
    width: '100%',
    zIndex: 1,
  },
  iconContainer: {
    position: 'relative',
    width: 28,
    height: 28,
    marginBottom: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabIcon: {
    width: 28,
    height: 28,
    zIndex: 1,
  },
  tabIconActive: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 28,
    height: 28,
    zIndex: 2,
  },
  tabLabel: {
    fontSize: 10,
    color: '#8B4513',
    marginTop: 2,
    fontWeight: '500',
    textAlign: 'center',
  },
  morePanelItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    marginHorizontal: 8,
    backgroundColor: Colors.lightOrange, // 使用Colors.js中的浅橙色
    borderRadius: 16,
    minHeight: 34,
  },
  morePanelText: {
    fontSize: 14,
    color: '#8B4513',
    fontWeight: '500',
    textAlign: 'center',
  },
});


