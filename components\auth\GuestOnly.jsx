import { useUser } from '../../hooks/useUser'
import { useRouter } from 'expo-router'
import { useEffect } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage'

import ThemedLoader from '../ThemedLoader'

const GuestOnly = ({ children }) => {
  const { user, authChecked } = useUser()
  const router = useRouter()

  useEffect(() => {
    const handleRedirect = async () => {
      if (authChecked && user !== null) {
        console.log('GuestOnly: 检测到已登录用户，检查是否是第一次登录');

        try {
          // 根据用户类型跳转到相应的默认页面
          if (user.isGuest) {
            console.log('GuestOnly: 游客用户 → dashboard index');
            router.replace("/(dashboard)/?tab=index");
          } else {
            console.log('GuestOnly: 正常用户 → dashboard plans');
            router.replace("/(dashboard)/plans?tab=plans");
          }
        } catch (error) {
          console.error('GuestOnly: 重定向逻辑出错:', error);
          // 出错时的默认行为：跳转到index页面
          router.replace("/(dashboard)/?tab=index");
        }
      }
    };

    handleRedirect();
  }, [user, authChecked])

  if (!authChecked || user) {
    return null; // 直接返回空，避免加载动画闪屏
  }

  return children
}

export default GuestOnly