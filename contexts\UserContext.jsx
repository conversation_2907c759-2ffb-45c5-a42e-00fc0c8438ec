import { createContext, useContext, useEffect, useState } from "react";
import AsyncStorage from '@react-native-async-storage/async-storage';
import apiServices from "../lib/apiServices"; // 使用新的统一API系统


export const UserContext = createContext();

export function UserProvider({ children }) {
  const [user, setUser] = useState(null);
  const [authChecked, setAuthChecked] = useState(false);
  const [profileSetupCompleted, setProfileSetupCompleted] = useState(false);
  const [recommendations, setRecommendations] = useState(null);

  async function register(email, password, checkPassword) {
    const result = await apiServices.auth.register(email, password, checkPassword);
    console.log('注册成功，结果:', result);

    // 新用户注册后，为该用户设置画像完成状态为false
    const userProfileKey = `profileSetupCompleted_${email}`;
    await AsyncStorage.setItem(userProfileKey, "false");
    console.log('新用户注册，设置画像完成状态为false，用户:', email);

    // 暂时不自动登录，等后端修复后再启用
    // return await login(email, password);

    return result;
  }

  async function login(userAccount, userPassword) {
    try {
      const loginResponse = await apiServices.auth.login(userAccount, userPassword);

      console.log("登录API原始响应:", loginResponse);

      // 解析登录响应数据结构
      let userData;
      if (loginResponse.data && loginResponse.data.userAccount) {
        // 标准结构：{code: 0, message: 'ok', data: {userAccount, ...}}
        userData = loginResponse.data;
      } else if (loginResponse.userAccount) {
        // 直接返回用户数据
        userData = loginResponse;
      } else {
        throw new Error('登录响应数据格式错误');
      }

      setUser(userData);

      console.log("解析后的用户数据:", userData);

      // ✅ 保存账号信息
      await AsyncStorage.setItem("userAccount", userData.userAccount);
      console.log('保存用户账号到 AsyncStorage:', userData.userAccount);

      // ✅ 处理人物画像状态
      let isCompleted = false;
      const userProfileKey = `profileSetupCompleted_${userData.userAccount}`;

      if (userData.profileSetupCompleted !== undefined) {
        isCompleted = userData.profileSetupCompleted;
        await AsyncStorage.setItem(userProfileKey, isCompleted ? "true" : "false");
        console.log('从服务器获取画像完成状态:', isCompleted, '用户:', userData.userAccount);
      } else {
        const setupCompleted = await AsyncStorage.getItem(userProfileKey);
        isCompleted = setupCompleted === "true"; // 如果没有存储值，默认为false
        console.log('从本地存储获取画像完成状态:', isCompleted, '用户:', userData.userAccount);
        // 如果本地也没有存储，确保设置为false（新用户）
        if (setupCompleted === null) {
          await AsyncStorage.setItem(userProfileKey, "false");
          console.log('本地无画像状态记录，设置为false，用户:', userData.userAccount);
        }
      }

      setProfileSetupCompleted(isCompleted);
      console.log('登录成功，用户数据:', userData, '人物画像完成状态:', isCompleted);

      return { userData, profileSetupCompleted: isCompleted };
    } catch (error) {
      console.error('登录过程中发生错误:', error);
      throw error;
    }
  }


  // 游客登录函数
  async function guestLogin() {
    const guestUser = {
      id: 'guest_' + Date.now(),
      userAccount: 'guest',
      userName: '游客用户',
      userAvatar: null,
      userProfile: null,
      userRole: 'guest',
      profileSetupCompleted: true, // 游客不需要设置画像
      isGuest: true
    };

    setUser(guestUser);
    setProfileSetupCompleted(true);
    console.log('游客登录成功:', guestUser);
    return guestUser;
  }

  async function logout(currentRoute = null) {
    // 保存当前页面路由，用于下次登录时恢复
    if (currentRoute) {
      await AsyncStorage.setItem('lastVisitedRoute', currentRoute);
      console.log('保存退出前的页面:', currentRoute);
    }

    setUser(null);
    // 重置当前的 profileSetupCompleted 状态，但不删除用户特定的存储数据
    // 下次登录时会从用户特定的存储中恢复正确的状态
    setProfileSetupCompleted(false);
    setRecommendations(null); // 清除推荐数据
    await AsyncStorage.removeItem("userAccount");
    await AsyncStorage.removeItem("auth_token"); // 清除 token

    // 清除游客会话数据
    await apiServices.auth.clearGuestSession();

    // 保留 profileSetupCompleted 状态，用户画像设置是永久的
    // 不清除用户特定的 profileSetupCompleted_${userAccount} 状态
    console.log('用户已退出登录，已清除游客会话，保留画像完成状态');
  }

  async function updateProfileSetupStatus(completed) {
    setProfileSetupCompleted(completed);

    // 获取当前用户账号
    const userAccount = await AsyncStorage.getItem("userAccount");
    if (userAccount) {
      const userProfileKey = `profileSetupCompleted_${userAccount}`;
      await AsyncStorage.setItem(userProfileKey, completed ? "true" : "false");
      console.log('更新人物画像状态:', completed, '用户:', userAccount);
    } else {
      console.warn('无法获取用户账号，无法更新画像状态');
    }
  }

  async function getInitialUserValue() {
    console.log('🚀 开始执行 getInitialUserValue');
    try {
      const userAccount = await AsyncStorage.getItem("userAccount");
      console.log('🔍 检查存储的用户账号:', userAccount);

      if (!userAccount) {
        console.log('没有存储的用户账号，检查游客会话');

        // 检查是否有游客会话
        const guestSession = await apiServices.auth.getGuestSession();
        if (guestSession && !await apiServices.auth.isGuestSessionExpired()) {
          console.log('静默恢复游客会话（不跳转页面）:', guestSession);
          setUser(guestSession);
          setProfileSetupCompleted(true);

          // 恢复缓存的推荐数据
          const cachedData = await apiServices.auth.getCachedGuestData();
          if (cachedData?.recommendations) {
            setRecommendations(cachedData.recommendations);
            console.log('恢复缓存的推荐数据');
          }

          // 后台刷新数据，但不影响当前页面
          apiServices.auth.preloadGuestData().then((freshData) => {
            if (freshData.recommendations && freshData.recommendations.length > 0) {
              setRecommendations(freshData.recommendations);
              console.log('后台更新推荐数据完成');
            }
          }).catch((error) => {
            console.warn('后台更新数据失败:', error);
          });

          setAuthChecked(true);
          return;
        }

        console.log('没有有效的游客会话，跳过自动登录');
        setAuthChecked(true);
        return;
      }

      // 先从本地存储恢复基本用户信息，保证登录状态
      const basicUserData = {
        id: Date.now(), // 使用时间戳作为临时ID
        userAccount: userAccount,
        userName: userAccount, // 使用账号作为默认用户名
        userAvatar: null,
        userProfile: null,
        userRole: 'user',
        profileSetupCompleted: false
      };

      // 检查用户是否已完成人物画像设置
      const userProfileKey = `profileSetupCompleted_${userAccount}`;
      const setupCompleted = await AsyncStorage.getItem(userProfileKey);
      const isCompleted = setupCompleted === "true"; // 如果没有存储值，默认为false
      basicUserData.profileSetupCompleted = isCompleted;

      // 如果本地没有存储画像状态，设置为false
      if (setupCompleted === null) {
        await AsyncStorage.setItem(userProfileKey, "false");
        console.log('本地无画像状态记录，设置为false，用户:', userAccount);
      }

      // 先设置基本用户信息，确保登录状态
      setUser(basicUserData);
      setProfileSetupCompleted(isCompleted);
      console.log('从本地存储恢复用户状态:', basicUserData);

      // 使用轻量级验证检查用户身份（只获取 id 和 userAccount）
      try {
        // 先检查是否有 token
        const token = await AsyncStorage.getItem("auth_token");
        console.log('检查存储的 token:', token ? `存在 (${token.substring(0, 20)}...)` : '不存在');

        // 同时检查所有存储的 keys 来调试
        const allKeys = await AsyncStorage.getAllKeys();
        console.log('AsyncStorage 中的所有 keys:', allKeys);

        if (!token) {
          console.log('❌ 没有找到 token，跳过服务器验证，保持本地登录状态');
          console.log('💡 提示：如需完整功能，建议重新登录获取有效认证信息');
        } else {
          console.log('🔍 开始轻量级用户身份验证（仅验证 id 和 userAccount）...');
          const basicValidation = await apiServices.auth.verifyUser();
          console.log('📋 轻量级验证结果:', basicValidation);

          if (basicValidation && (basicValidation.id || basicValidation.userAccount)) {
            // ✅ 验证成功，更新必要信息
            let hasUpdates = false;

            if (basicValidation.id && basicValidation.id !== basicUserData.id) {
              const updatedUserData = {
                ...basicUserData,
                id: basicValidation.id
              };
              setUser(updatedUserData);
              console.log('🔄 更新用户ID为服务器数据:', updatedUserData.id);
              hasUpdates = true;
            }

            // 验证用户账号是否匹配
            if (basicValidation.userAccount && basicValidation.userAccount !== userAccount) {
              console.warn('⚠️ 服务器返回的用户账号与本地存储不匹配');
              console.warn('本地:', userAccount, '服务器:', basicValidation.userAccount);
            }

            console.log(`✅ 轻量级用户验证成功${hasUpdates ? '，已更新用户信息' : '，信息一致'}`);
          } else {
            console.log('❌ 轻量级用户验证失败，但保持本地登录状态');
            console.log('💡 用户可继续使用应用，部分功能可能受限');
          }
        }
      } catch (validationError) {
        console.log('轻量级用户验证出错，保持本地登录状态:', validationError.message);
        // 验证出错但不影响用户体验，继续使用本地存储的信息
      }

    } catch (e) {
      console.error("❌ 恢复用户状态失败", e);
      // 即使出错也不清除本地存储，尝试保持登录状态
      const userAccount = await AsyncStorage.getItem("userAccount");
      if (userAccount) {
        const fallbackUserData = {
          id: Date.now(),
          userAccount: userAccount,
          userName: userAccount,
          userAvatar: null,
          userProfile: null,
          userRole: 'user',
          profileSetupCompleted: false
        };
        setUser(fallbackUserData);
        console.log('🔄 使用备用用户数据保持登录状态:', fallbackUserData);
      } else {
        setUser(null);
        console.log('❌ 没有存储的用户账号，设置用户为null');
      }
    }
    console.log('✅ getInitialUserValue 执行完成，设置 authChecked 为 true');
    setAuthChecked(true);
  }

  useEffect(() => {
    getInitialUserValue();
  }, []);

  return (
    <UserContext.Provider
      value={{
        user,
        setUser,
        login,
        guestLogin,
        logout,
        register,
        authChecked,
        profileSetupCompleted,
        setProfileSetupCompleted,
        updateProfileSetupStatus,
        recommendations,
        setRecommendations,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

// Hook for using recommendation functionality
export const useRecommendation = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useRecommendation must be used within a UserProvider');
  }
  return {
    recommendations: context.recommendations,
    setRecommendations: context.setRecommendations,
  };
};
